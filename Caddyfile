# Caddyfile for TERALIS website
# <PERSON><PERSON><PERSON> automatically handles HTTPS with Let's Encrypt

# Production configuration with automatic HTTPS
teralis.ch, www.teralis.ch {
    # Root directory for static files
    root * /

    # Enable gzip compression
    encode gzip

    # Security headers
    header {
        # Prevent clickjacking
        X-Frame-Options "SAMEORIGIN"
        
        # Prevent MIME type sniffing
        X-Content-Type-Options "nosniff"
        
        # XSS protection
        X-XSS-Protection "1; mode=block"
        
        # Referrer policy
        Referrer-Policy "no-referrer-when-downgrade"
        
        # Permissions policy
        Permissions-Policy "geolocation=(), microphone=(), camera=()"
        
        # Remove Server header
        -Server
    }

    # Cache static assets
    @static {
        path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
    }
    header @static Cache-Control "public, max-age=31536000, immutable"

    # Health check endpoint
    handle /health {
        respond "healthy" 200
    }

    # SPA fallback - serve index.html for all routes
    try_files {path} /index.html

    # Serve static files
    file_server

    # Logging
    log {
        output file /var/log/caddy/access.log
        format json
    }
}

# Development/local configuration (when not using domain)
:80 {
    root * /srv
    encode gzip
    
    header {
        X-Frame-Options "SAMEORIGIN"
        X-Content-Type-Options "nosniff"
        X-XSS-Protection "1; mode=block"
        -Server
    }

    @static {
        path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
    }
    header @static Cache-Control "public, max-age=31536000, immutable"

    handle /health {
        respond "healthy" 200
    }

    try_files {path} /index.html
    file_server
}


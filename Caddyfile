# Simplified <PERSON><PERSON><PERSON><PERSON><PERSON> for TERALIS website
# <PERSON><PERSON><PERSON> automatically handles HTTPS with Let's Encrypt and HTTP->HTTPS redirect

# Main site configuration - <PERSON><PERSON><PERSON> will automatically redirect HTTP to HTTPS
teralis.ch, www.teralis.ch {
    # Root directory for static files
    root * /srv

    # Enable compression
    encode gzip

    # Basic security headers
    header {
        X-Frame-Options "SAMEORIGIN"
        X-Content-Type-Options "nosniff"
        -Server
    }

    # Cache static assets (including logos and images)
    @static {
        path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot *.webp
    }
    header @static Cache-Control "public, max-age=86400"

    # Health check
    handle /health {
        respond "healthy" 200
    }

    # Serve files with SPA fallback
    try_files {path} /index.html
    file_server

    # Simple logging
    log

    tls {
		on_demand
	}

}


---
# Ansible Playbook for TERALIS Website Deployment
# Usage: ansible-playbook -i inventory.yml deploy.yml

- name: Build Docker image locally
  hosts: localhost
  connection: local
  gather_facts: false

  tasks:
    - name: Build Docker image locally
      community.docker.docker_image:
        name: teralis-website
        tag: latest
        build:
          path: "{{ playbook_dir }}/.."
          dockerfile: Dockerfile
        source: build
        state: present

    - name: Save Docker image to tar file
      community.docker.docker_image:
        name: teralis-website:latest
        archive_path: "{{ playbook_dir }}/teralis-website.tar"
        source: local
        state: present

- name: Deploy TERALIS Website with Docker
  hosts: production
  become: true

  tasks:
    - name: Wait for automatic system updates to complete
      shell: while pgrep -x apt-get > /dev/null; do sleep 5; done
      changed_when: false

    - name: Wait for dpkg lock to be released
      shell: while fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1; do sleep 5; done
      changed_when: false
      ignore_errors: true

    - name: Wait for apt lock to be released
      shell: while fuser /var/lib/apt/lists/lock >/dev/null 2>&1; do sleep 5; done
      changed_when: false
      ignore_errors: true
      
    - name: Update APT packages
      apt:
        update_cache: true
        upgrade: dist
      retries: 3
      delay: 5
      ignore_errors: true

    - name: Install snapd (if not already installed)
      apt:
        name: snapd
        state: present

    - name: Ensure snap is up to date
      command: snap install core
      register: snap_core_install
      changed_when: "'is already installed' not in snap_core_install.stdout"

    - name: Install Docker via snap
      command: snap install docker

    - name: Stop running containers. Ignore errors if it is not a first deployment
      command:
        cmd: docker compose down -v
        chdir: /opt/teralis
      ignore_errors: true

    - name: Remove current code directory
      file:
        path: /opt/teralis
        state: absent

    - name: Install prerequisites
      apt:
        name:
          - wget
          - apt-transport-https
          - gpg
        state: present
        update_cache: true

    - name: Create deployment directory
      file:
        path: /opt/teralis
        state: directory
        mode: '0755'

    - name: Copy Docker image to server
      copy:
        src: "{{ playbook_dir }}/teralis-website.tar"
        dest: /opt/teralis/teralis-website.tar
        mode: '0644'

    - name: Copy docker-compose.yml to server
      copy:
        src: "{{ playbook_dir }}/../docker-compose.yml"
        dest: /opt/teralis/docker-compose.yml
        mode: '0644'

    - name: Copy Caddyfile to server
      copy:
        src: "{{ playbook_dir }}/../Caddyfile"
        dest: /opt/teralis/Caddyfile
        mode: '0644'

    - name: Load Docker image on server
      command:
        cmd: docker load -i teralis-website.tar
        chdir: /opt/teralis

    - name: Set correct ownership and permissions for the code recursively
      file:
        path: /opt/teralis
        state: directory
        owner: 1000
        group: 1000
        mode: '0755'
        recurse: yes

    - name: Deploy Docker containers
      command:
        cmd: docker compose up -d
        chdir: /opt/teralis

    - name: Clean up Docker image tar file on server
      file:
        path: /opt/teralis/teralis-website.tar
        state: absent

- name: Clean up local files
  hosts: localhost
  connection: local
  gather_facts: false

  tasks:
    - name: Clean up local Docker image tar file
      file:
        path: "{{ playbook_dir }}/teralis-website.tar"
        state: absent

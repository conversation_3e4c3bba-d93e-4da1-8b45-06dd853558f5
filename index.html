<!doctype html>
<html lang="fr">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/png" href="/teralis.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Primary Meta Tags -->
  <title>TERALIS - Services Professionnels à Lausanne | Nettoyage, Logistique & Administration</title>
  <meta name="title" content="TERALIS - Services Professionnels à Lausanne | Nettoyage, Logistique & Administration" />
  <meta name="description"
    content="TERALIS offre des solutions pratiques et fiables pour les entreprises et particuliers à Lausanne : nettoyage, logistique, maintenance légère et services administratifs depuis 2021." />
  <meta name="keywords"
    content="TERALIS, Lausanne, nettoyage professionnel, logistique, maintenance, services administratifs, conciergerie, Suisse" />
  <meta name="author" content="TERALIS" />
  <meta name="robots" content="index, follow" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://teralis.ch/" />
  <meta property="og:title" content="TERALIS - Services Professionnels à Lausanne" />
  <meta property="og:description"
    content="Votre partenaire de confiance pour tous vos besoins quotidiens : nettoyage, logistique et services administratifs." />
  <meta property="og:image" content="/teralis.png" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://teralis.ch/" />
  <meta property="twitter:title" content="TERALIS - Services Professionnels à Lausanne" />
  <meta property="twitter:description"
    content="Votre partenaire de confiance pour tous vos besoins quotidiens : nettoyage, logistique et services administratifs." />
  <meta property="twitter:image" content="/teralis.png" />

  <!-- Additional Meta Tags -->
  <meta name="theme-color" content="#1e3a5f" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

  <!-- Preconnect to Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>
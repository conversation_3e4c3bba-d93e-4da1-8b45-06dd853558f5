version: '3.8'

services:
  teralis-web:
    image: teralis-website:latest
    container_name: teralis-website
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
    volumes:
      # Persist Caddy data (certificates, etc.)
      - caddy_data:/data
      - caddy_config:/config
    networks:
      - teralis-network

volumes:
  caddy_data:
    driver: local
  caddy_config:
    driver: local

networks:
  teralis-network:
    driver: bridge

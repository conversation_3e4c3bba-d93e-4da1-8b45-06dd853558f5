# TERALIS Website - Deployment Guide

This guide covers all deployment options for the TERALIS website.

## 📋 Table of Contents

- [Option 1: Ansible Automated Deployment (Recommended)](#option-1-ansible-automated-deployment-recommended)
- [Option 2: Manual Docker Deployment](#option-2-manual-docker-deployment)
- [Option 3: Cloud Platforms](#option-3-cloud-platforms)

---

## Option 1: Ansible Automated Deployment (Recommended)

**Best for:** Production deployments with automated setup and updates.

### ✨ Features

- ✅ Fully automated deployment
- ✅ Docker and Caddy installation
- ✅ Automatic HTTPS with Let's Encrypt
- ✅ Firewall configuration
- ✅ One-command updates
- ✅ Rollback support
- ✅ Health monitoring

### 📋 Prerequisites

- Ubuntu 20.04+ or Debian 11+ server
- SSH access with sudo privileges
- Ansible installed on your local machine
- Domain name pointing to your server

### 🚀 Quick Start

#### 1. Install Ansible (on your local machine)

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install ansible
```

**macOS:**
```bash
brew install ansible
```

**Windows (WSL):**
```bash
sudo apt update && sudo apt install ansible
```

#### 2. Setup Ansible

```bash
cd ansible
./setup.sh  # Linux/macOS
# or
.\setup.ps1  # Windows PowerShell
```

#### 3. Configure Inventory

Edit `ansible/inventory.yml`:

```yaml
teralis_server:
  ansible_host: YOUR_SERVER_IP      # Replace with your server IP
  ansible_user: YOUR_SSH_USER       # Replace with your SSH user
  domain_name: teralis.ch
  email: <EMAIL>
```

#### 4. Test Connection

```bash
cd ansible
ansible -i inventory.yml production -m ping
```

#### 5. Deploy

```bash
ansible-playbook -i inventory.yml deploy.yml
```

This will take 5-10 minutes and automatically:
- Install Docker and Docker Compose
- Configure firewall (ports 22, 80, 443)
- Create deployment user
- Copy and build application
- Start containers with automatic HTTPS

#### 6. Verify

Visit `https://teralis.ch` - Your website is live! 🎉

### 📚 Ansible Commands

**Update website:**
```bash
ansible-playbook -i inventory.yml update.yml
```

**Check status:**
```bash
ansible-playbook -i inventory.yml monitoring.yml
```

**Backup data:**
```bash
ansible-playbook -i inventory.yml backup.yml
```

**Rollback:**
```bash
ansible-playbook -i inventory.yml rollback.yml
```

### 📖 Full Documentation

See [ansible/README.md](ansible/README.md) for complete documentation.

---

## Option 2: Manual Docker Deployment

**Best for:** Manual control or custom server configurations.

### 📋 Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- Domain name pointing to your server
- Ports 80 and 443 open

### 🚀 Quick Start

#### 1. Install Docker

**Ubuntu/Debian:**
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

#### 2. Install Docker Compose

```bash
sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.5/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 3. Copy Files to Server

```bash
# From your local machine
scp -r . user@your-server:/opt/teralis
```

#### 4. Build and Deploy

```bash
# On the server
cd /opt/teralis
docker-compose up -d --build
```

#### 5. Verify

```bash
# Check containers
docker ps

# Check logs
docker-compose logs -f

# Test health
curl http://localhost/health
```

### 🔄 Updates

```bash
cd /opt/teralis
git pull  # or copy new files
docker-compose up -d --build
docker image prune -f
```

### 📖 Full Documentation

See [CADDY_DEPLOYMENT.md](CADDY_DEPLOYMENT.md) for complete Docker/Caddy documentation.

---

## Option 3: Cloud Platforms

### Netlify

**Best for:** Quick deployments without server management.

#### Deploy

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Build
npm run build

# Deploy
netlify deploy --prod --dir=dist
```

**Note:** Configure domain and HTTPS in Netlify dashboard.

### Vercel

**Best for:** Next.js or serverless deployments.

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel --prod
```

### AWS / Google Cloud / Azure

For cloud deployments:

1. **Build locally:**
   ```bash
   npm run build
   ```

2. **Upload `dist/` folder** to:
   - AWS S3 + CloudFront
   - Google Cloud Storage + CDN
   - Azure Blob Storage + CDN

3. **Configure HTTPS** via cloud provider's certificate manager

---

## 🔒 DNS Configuration

For all deployment options, configure DNS:

```
A Record:     teralis.ch      →  YOUR_SERVER_IP
A Record:     www.teralis.ch  →  YOUR_SERVER_IP
```

Or for cloud platforms, use their provided DNS records.

---

## 🔐 Security Checklist

Before going live:

- [ ] HTTPS enabled (automatic with Caddy)
- [ ] Firewall configured (ports 22, 80, 443 only)
- [ ] SSH key authentication enabled
- [ ] Regular backups scheduled
- [ ] Monitoring set up
- [ ] DNS configured correctly
- [ ] SSL certificate obtained
- [ ] Health check endpoint working

---

## 📊 Monitoring

### Health Check

```bash
curl https://teralis.ch/health
# Expected: "healthy"
```

### Container Status

```bash
docker ps
docker-compose logs -f
```

### SSL Certificate

```bash
docker exec teralis-website caddy list-certificates
```

---

## 🆘 Troubleshooting

### Images Not Loading

**Problem:** Images show broken in production

**Solution:** Images are now in `public/` folder and will work after rebuild:
```bash
docker-compose up -d --build
```

### Port Already in Use

**Problem:** Port 80 or 443 in use

**Solution:**
```bash
sudo lsof -i :80
sudo lsof -i :443
sudo systemctl stop apache2  # or nginx
```

### Certificate Not Obtained

**Problem:** HTTPS not working

**Solution:**
1. Verify DNS points to server: `nslookup teralis.ch`
2. Check Caddy logs: `docker-compose logs`
3. Ensure ports 80 and 443 are open

### Container Won't Start

**Problem:** Container exits immediately

**Solution:**
```bash
docker-compose logs
docker-compose down
docker-compose up -d --build
```

---

## 🔄 Backup & Restore

### Backup

**With Ansible:**
```bash
ansible-playbook -i inventory.yml backup.yml
```

**Manual:**
```bash
# Backup Caddy certificates
docker run --rm -v teralis_caddy_data:/data -v $(pwd):/backup alpine tar czf /backup/caddy-backup.tar.gz /data

# Backup application
tar czf teralis-app-backup.tar.gz /opt/teralis
```

### Restore

```bash
# Restore Caddy certificates
docker run --rm -v teralis_caddy_data:/data -v $(pwd):/backup alpine tar xzf /backup/caddy-backup.tar.gz -C /

# Restart
docker-compose restart
```

---

## 📚 Additional Resources

- **Ansible Documentation:** [ansible/README.md](ansible/README.md)
- **Caddy Documentation:** [CADDY_DEPLOYMENT.md](CADDY_DEPLOYMENT.md)
- **Quick Start:** [QUICK_START.md](QUICK_START.md)

---

## 🎉 Success!

Once deployed, your website will be available at:
- **https://teralis.ch** (with automatic HTTPS)
- **https://www.teralis.ch** (with automatic HTTPS)

For support: <EMAIL>

---

© 2025 TERALIS SA. All rights reserved.


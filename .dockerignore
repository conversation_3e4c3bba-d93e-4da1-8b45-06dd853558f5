# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build output
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore
nginx.conf

# Documentation
README.md
*.md

# Environment files
.env
.env.local
.env.*.local

# Testing
coverage
.nyc_output

# Logs
logs
*.log


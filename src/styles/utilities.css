/* Container */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

@media (max-width: 640px) {
  .container {
    padding: 0 var(--spacing-md);
  }
}

/* Section */
.section {
  padding: var(--spacing-4xl) 0;
}

@media (max-width: 768px) {
  .section {
    padding: var(--spacing-3xl) 0;
  }
}

@media (max-width: 640px) {
  .section {
    padding: var(--spacing-2xl) 0;
  }
}

/* Section Title */
.section-title {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-title h2 {
  margin-bottom: var(--spacing-md);
}

.section-title p {
  font-size: var(--text-lg);
  color: var(--color-gray-500);
  max-width: 600px;
  margin: 0 auto;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--text-base);
  transition: all var(--transition-base);
  text-decoration: none;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: var(--color-green);
  color: var(--color-white);
}

.btn-primary:hover {
  background-color: var(--color-green-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background-color: var(--color-white);
  color: var(--color-navy);
  border: 2px solid var(--color-white);
}

.btn-secondary:hover {
  background-color: transparent;
  color: var(--color-white);
  border-color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-navy);
  border: 2px solid var(--color-navy);
}

.btn-outline:hover {
  background-color: var(--color-navy);
  color: var(--color-white);
}

.btn-lg {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--text-lg);
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--text-sm);
}

/* Card */
.card {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Grid */
.grid {
  display: grid;
  gap: var(--spacing-xl);
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 1024px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {

  .grid-cols-4,
  .grid-cols-3,
  .grid-cols-2 {
    grid-template-columns: repeat(1, 1fr);
  }
}

/* Flex Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

.gap-xl {
  gap: var(--spacing-xl);
}

/* Text Utilities */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* Visibility */
.hidden {
  display: none;
}

.visible {
  display: block;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
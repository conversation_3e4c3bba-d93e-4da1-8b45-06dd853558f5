/* Contact Section */
.contact-section {
  background-color: var(--color-gray-50);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: var(--spacing-3xl);
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.contact-info-title {
  font-size: var(--text-3xl);
  color: var(--color-navy);
  margin-bottom: var(--spacing-lg);
}

.contact-info-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.contact-info-item {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.contact-info-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateX(4px);
}

.contact-info-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-green) 0%, var(--color-green-light) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  flex-shrink: 0;
}

.contact-info-content h3 {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
}

.contact-info-content a,
.contact-info-content p {
  font-size: var(--text-lg);
  color: var(--color-navy);
  font-weight: 500;
  margin: 0;
}

.contact-info-content a {
  transition: color var(--transition-fast);
}

.contact-info-content a:hover {
  color: var(--color-green);
}

/* Map Container */
.map-container {
  width: 100%;
  height: 300px;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-navy-light) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  color: var(--color-white);
}

.map-placeholder p {
  font-size: var(--text-lg);
  font-weight: 500;
  margin: 0;
}

/* Contact Form */
.contact-form-container {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
}

.contact-form-title {
  font-size: var(--text-3xl);
  color: var(--color-navy);
  margin-bottom: var(--spacing-2xl);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-label {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--color-gray-700);
}

.form-input {
  padding: var(--spacing-md);
  border: 2px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  font-family: var(--font-primary);
  color: var(--color-gray-800);
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-green);
  box-shadow: 0 0 0 3px rgba(124, 179, 66, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-error {
  font-size: var(--text-sm);
  color: #ef4444;
  margin-top: calc(var(--spacing-xs) * -1);
}

.form-submit {
  margin-top: var(--spacing-md);
  width: 100%;
}

.form-message {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  font-weight: 500;
  text-align: center;
}

.form-message.success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #6ee7b7;
}

.form-message.error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Responsive */
@media (max-width: 1024px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .map-container {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .contact-info-title,
  .contact-form-title {
    font-size: var(--text-2xl);
  }

  .contact-form-container {
    padding: var(--spacing-xl);
  }

  .contact-info-item {
    padding: var(--spacing-md);
  }

  .contact-info-icon {
    width: 40px;
    height: 40px;
  }

  .contact-info-icon svg {
    width: 20px;
    height: 20px;
  }

  .contact-info-content a,
  .contact-info-content p {
    font-size: var(--text-base);
  }
}

@media (max-width: 640px) {
  .contact-form-container {
    padding: var(--spacing-lg);
  }

  .form-input {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .map-container {
    height: 200px;
  }
}


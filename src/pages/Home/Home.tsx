import { useTranslation } from 'react-i18next';
import { Sparkles, Package, FileText, ArrowRight } from 'lucide-react';
import './Home.css';

interface HomeProps {
  onNavigate: (page: string) => void;
}

const Home = ({ onNavigate }: HomeProps) => {
  const { t } = useTranslation();

  const services = [
    {
      icon: <Sparkles size={40} />,
      title: t('services.cleaning.title'),
      description: t('services.cleaning.description'),
      key: 'cleaning',
    },
    {
      icon: <Package size={40} />,
      title: t('services.logistics.title'),
      description: t('services.logistics.description'),
      key: 'logistics',
    },
    {
      icon: <FileText size={40} />,
      title: t('services.administrative.title'),
      description: t('services.administrative.description'),
      key: 'administrative',
    },
  ];

  const values = [
    {
      title: t('values.reliability.title'),
      description: t('values.reliability.description'),
    },
    {
      title: t('values.responsiveness.title'),
      description: t('values.responsiveness.description'),
    },
    {
      title: t('values.trust.title'),
      description: t('values.trust.description'),
    },
    {
      title: t('values.versatility.title'),
      description: t('values.versatility.description'),
    },
  ];

  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-background"></div>
        <div className="container">
          <div className="hero-content">
            <h1 className="hero-title animate-fade-in-up">
              {t('hero.title')}
            </h1>
            <p className="hero-subtitle animate-fade-in-up">
              {t('hero.subtitle')}
            </p>
            <p className="hero-tagline animate-fade-in-up">
              {t('hero.tagline')}
            </p>
            <div className="hero-actions animate-fade-in-up">
              <button
                className="btn btn-primary btn-lg"
                onClick={() => onNavigate('services')}
              >
                {t('hero.cta')}
                <ArrowRight size={20} />
              </button>
              <button
                className="btn btn-secondary btn-lg"
                onClick={() => onNavigate('contact')}
              >
                {t('hero.ctaContact')}
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="section services-section">
        <div className="container">
          <div className="section-title">
            <h2>{t('services.title')}</h2>
            <p>{t('services.subtitle')}</p>
          </div>
          <div className="services-grid">
            {services.map((service, index) => (
              <div
                key={service.key}
                className="service-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="service-icon">{service.icon}</div>
                <h3 className="service-title">{service.title}</h3>
                <p className="service-description">{service.description}</p>
                <button
                  className="service-link"
                  onClick={() => onNavigate('services')}
                >
                  {t('services.learnMore')}
                  <ArrowRight size={18} />
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="section values-section">
        <div className="container">
          <div className="section-title">
            <h2>{t('values.title')}</h2>
          </div>
          <div className="values-grid">
            {values.map((value, index) => (
              <div
                key={value.title}
                className="value-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="value-number">{String(index + 1).padStart(2, '0')}</div>
                <h3 className="value-title">{value.title}</h3>
                <p className="value-description">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section cta-section">
        <div className="container">
          <div className="cta-content">
            <h2 className="cta-title">{t('common.getStarted')}</h2>
            <p className="cta-description">
              {t('hero.tagline')}
            </p>
            <button
              className="btn btn-primary btn-lg"
              onClick={() => onNavigate('contact')}
            >
              {t('common.contactUs')}
              <ArrowRight size={20} />
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;


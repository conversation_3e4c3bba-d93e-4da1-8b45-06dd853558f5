/* Hero Section */
.hero {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-image: url('/lavaux.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(30, 58, 95, 0.5) 0%, rgba(30, 58, 95, 0.4) 100%);
  z-index: 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 50%, rgba(217, 119, 51, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(217, 119, 51, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
  padding: var(--spacing-2xl) 0;
}

.hero-title {
  font-size: var(--text-6xl);
  font-weight: 700;
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: var(--text-2xl);
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-weight: 500;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-tagline {
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: var(--spacing-2xl);
  line-height: 1.8;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.hero-actions {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
  flex-wrap: wrap;
}

/* Services Section */
.services-section {
  background-color: var(--color-gray-50);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2xl);
}

.service-card {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
  animation: fadeInUp 0.6s ease-out both;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-green) 0%, var(--color-green-light) 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
  transition: transform var(--transition-base);
}

.service-card:hover .service-icon {
  transform: scale(1.1) rotate(5deg);
}

.service-title {
  font-size: var(--text-xl);
  color: var(--color-navy);
  margin-bottom: var(--spacing-md);
}

.service-description {
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-lg);
  flex-grow: 1;
}

.service-link {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: none;
  color: var(--color-green);
  font-weight: 600;
  padding: 0;
  transition: gap var(--transition-fast);
}

.service-link:hover {
  gap: var(--spacing-md);
}

/* Values Section */
.values-section {
  background-color: var(--color-white);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
}

.value-card {
  text-align: center;
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  animation: fadeInUp 0.6s ease-out both;
}

.value-card:hover {
  background-color: var(--color-gray-50);
  transform: translateY(-4px);
}

.value-number {
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--color-green);
  opacity: 0.3;
  margin-bottom: var(--spacing-md);
  font-family: var(--font-heading);
}

.value-title {
  font-size: var(--text-xl);
  color: var(--color-navy);
  margin-bottom: var(--spacing-sm);
}

.value-description {
  color: var(--color-gray-600);
  font-size: var(--text-base);
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-navy-dark) 100%);
  border-radius: var(--radius-2xl);
  margin: 0 var(--spacing-lg);
}

.cta-content {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-2xl);
}

.cta-title {
  font-size: var(--text-4xl);
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.cta-description {
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-2xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive */
@media (max-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .values-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero {
    min-height: 55vh;
  }

  .hero-title {
    font-size: var(--text-4xl);
  }

  .hero-subtitle {
    font-size: var(--text-xl);
  }

  .hero-tagline {
    font-size: var(--text-base);
  }

  .hero-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .values-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .cta-title {
    font-size: var(--text-3xl);
  }

  .cta-description {
    font-size: var(--text-base);
  }
}

@media (max-width: 640px) {
  .hero {
    min-height: 50vh;
  }

  .hero-content {
    padding: var(--spacing-xl) 0;
  }

  .hero-title {
    font-size: var(--text-3xl);
    letter-spacing: 1px;
  }

  .service-card {
    padding: var(--spacing-xl);
  }

  .service-icon {
    width: 60px;
    height: 60px;
  }

  .cta-section {
    margin: 0;
    border-radius: 0;
  }

  .cta-content {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }
}
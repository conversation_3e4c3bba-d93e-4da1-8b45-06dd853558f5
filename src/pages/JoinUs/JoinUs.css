/* Intro Section */
.join-intro {
  background-color: var(--color-white);
}

.intro-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.intro-text {
  font-size: var(--text-xl);
  color: var(--color-gray-700);
  line-height: 1.8;
}

/* Opportunities Section */
.opportunities-section {
  background-color: var(--color-gray-50);
}

.opportunities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-2xl);
}

.opportunity-card {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
  text-align: center;
}

.opportunity-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.opportunity-icon {
  width: 100px;
  height: 100px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  margin: 0 auto var(--spacing-xl);
  transition: transform var(--transition-base);
}

.opportunity-card:hover .opportunity-icon {
  transform: scale(1.1) rotate(5deg);
}

.opportunity-icon.careers {
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-navy-light) 100%);
}

.opportunity-icon.partnerships {
  background: linear-gradient(135deg, var(--color-green) 0%, var(--color-green-light) 100%);
}

.opportunity-title {
  font-size: var(--text-2xl);
  color: var(--color-navy);
  margin-bottom: var(--spacing-md);
}

.opportunity-description {
  font-size: var(--text-base);
  color: var(--color-gray-600);
  line-height: 1.7;
}

/* Benefits Section */
.benefits-section {
  background-color: var(--color-white);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  max-width: 1000px;
  margin: 0 auto;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background-color: var(--color-gray-50);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  animation: fadeInUp 0.6s ease-out both;
}

.benefit-item:hover {
  background-color: var(--color-green-light);
  transform: translateX(8px);
}

.benefit-check {
  flex-shrink: 0;
  color: var(--color-green);
  transition: color var(--transition-fast);
}

.benefit-item:hover .benefit-check {
  color: var(--color-white);
}

.benefit-text {
  font-size: var(--text-base);
  color: var(--color-gray-700);
  font-weight: 500;
  transition: color var(--transition-fast);
}

.benefit-item:hover .benefit-text {
  color: var(--color-white);
}

/* Join CTA */
.join-cta {
  background-color: var(--color-gray-50);
}

.join-cta .cta-box {
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-navy-dark) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-3xl);
  text-align: center;
}

.cta-icon {
  color: var(--color-green-light);
  margin-bottom: var(--spacing-lg);
}

.join-cta .cta-box-title {
  font-size: var(--text-4xl);
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.join-cta .cta-box-description {
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-2xl);
}

/* Job Openings Section */
.job-openings-section {
  background-color: var(--color-white);
}

.jobs-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

/* Job Card */
.job-card {
  background-color: var(--color-white);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  transition: all var(--transition-base);
  animation: fadeInUp 0.6s ease-out both;
  display: flex;
  flex-direction: column;
}

.job-card:hover {
  border-color: var(--color-green);
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

/* Job Header */
.job-header {
  margin-bottom: var(--spacing-xl);
}

.job-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-navy-light) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
  transition: transform var(--transition-base);
}

.job-card:hover .job-icon {
  transform: scale(1.1);
}

.job-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--color-navy);
  margin-bottom: var(--spacing-md);
}

.job-meta {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.job-meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-sm);
  color: var(--color-gray-600);
}

.job-meta-item svg {
  color: var(--color-green);
}

/* Job Body */
.job-body {
  flex: 1;
  margin-bottom: var(--spacing-xl);
}

.job-description {
  font-size: var(--text-base);
  color: var(--color-gray-700);
  line-height: 1.8;
  margin-bottom: var(--spacing-xl);
}

.job-section {
  margin-bottom: var(--spacing-lg);
}

.job-section h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-navy);
  margin-bottom: var(--spacing-md);
}

.job-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.job-list li {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  line-height: 1.6;
}

.job-list li svg {
  color: var(--color-green);
  flex-shrink: 0;
  margin-top: 2px;
}

/* Job Footer */
.job-footer {
  border-top: 1px solid var(--color-gray-200);
  padding-top: var(--spacing-lg);
}

.job-footer .btn {
  width: 100%;
  justify-content: center;
}

/* Responsive */
@media (max-width: 1024px) {
  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .jobs-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .intro-text {
    font-size: var(--text-lg);
  }

  .opportunities-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .opportunity-icon {
    width: 80px;
    height: 80px;
  }

  .opportunity-icon svg {
    width: 32px;
    height: 32px;
  }

  .opportunity-title {
    font-size: var(--text-xl);
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .join-cta .cta-box {
    padding: var(--spacing-2xl);
  }

  .join-cta .cta-box-title {
    font-size: var(--text-3xl);
  }

  .join-cta .cta-box-description {
    font-size: var(--text-base);
  }
}

@media (max-width: 640px) {
  .benefit-item {
    padding: var(--spacing-md);
  }

  .benefit-text {
    font-size: var(--text-sm);
  }

  .benefit-item:hover {
    transform: translateX(4px);
  }
}
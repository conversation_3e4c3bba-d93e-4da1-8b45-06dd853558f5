import { useTranslation } from 'react-i18next';
import { Briefcase, Handshake, Check, Mail, MapPin, Clock, ArrowRight } from 'lucide-react';
import './JoinUs.css';

interface JoinUsProps {
  onNavigate: (page: string) => void;
}

const JoinUs = ({ onNavigate }: JoinUsProps) => {
  const { t } = useTranslation();

  const benefits = t('joinUs.benefits.items', { returnObjects: true }) as string[];

  const jobOpenings = t('jobs.openings', { returnObjects: true }) as Array<{
    title: string;
    location: string;
    type: string;
    description: string;
    responsibilities: string[];
    requirements: string[];
  }>;

  return (
    <div className="join-us-page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <h1 className="page-title animate-fade-in-up">{t('joinUs.title')}</h1>
          <p className="page-subtitle animate-fade-in-up">{t('joinUs.subtitle')}</p>
        </div>
      </section>

      {/* Intro Section */}
      <section className="section join-intro">
        <div className="container">
          <div className="intro-content">
            <p className="intro-text">{t('joinUs.intro')}</p>
          </div>
        </div>
      </section>

      {/* Opportunities Section */}
      <section className="section opportunities-section">
        <div className="container">
          <div className="section-title">
            <h2>{t('joinUs.opportunities.title')}</h2>
          </div>
          <div className="opportunities-grid">
            <div className="opportunity-card">
              <div className="opportunity-icon careers">
                <Briefcase size={40} />
              </div>
              <h3 className="opportunity-title">
                {t('joinUs.opportunities.careers.title')}
              </h3>
              <p className="opportunity-description">
                {t('joinUs.opportunities.careers.description')}
              </p>
            </div>
            <div className="opportunity-card">
              <div className="opportunity-icon partnerships">
                <Handshake size={40} />
              </div>
              <h3 className="opportunity-title">
                {t('joinUs.opportunities.partnerships.title')}
              </h3>
              <p className="opportunity-description">
                {t('joinUs.opportunities.partnerships.description')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="section benefits-section">
        <div className="container">
          <div className="section-title">
            <h2>{t('joinUs.benefits.title')}</h2>
          </div>
          <div className="benefits-grid">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="benefit-item"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <Check size={24} className="benefit-check" />
                <span className="benefit-text">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Job Openings Section */}
      <section className="section job-openings-section">
        <div className="container">
          <div className="section-title">
            <h2>{t('jobs.title')}</h2>
            <p>{t('jobs.subtitle')}</p>
          </div>
          <div className="jobs-grid">
            {jobOpenings.map((job, index) => (
              <div
                key={job.title}
                className="job-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="job-header">
                  <div className="job-icon">
                    <Briefcase size={32} />
                  </div>
                  <h3 className="job-title">{job.title}</h3>
                  <div className="job-meta">
                    <span className="job-meta-item">
                      <MapPin size={16} />
                      {job.location}
                    </span>
                    <span className="job-meta-item">
                      <Clock size={16} />
                      {job.type}
                    </span>
                  </div>
                </div>

                <div className="job-body">
                  <p className="job-description">{job.description}</p>

                  <div className="job-section">
                    <h4>{t('jobs.responsibilitiesLabel')}</h4>
                    <ul className="job-list">
                      {job.responsibilities.map((item, idx) => (
                        <li key={idx}>
                          <Check size={16} />
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="job-section">
                    <h4>{t('jobs.requirementsLabel')}</h4>
                    <ul className="job-list">
                      {job.requirements.map((item, idx) => (
                        <li key={idx}>
                          <Check size={16} />
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="job-footer">
                  <button
                    className="btn btn-primary"
                    onClick={() => onNavigate('contact')}
                  >
                    {t('jobs.applyButton')}
                    <ArrowRight size={18} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section join-cta">
        <div className="container">
          <div className="cta-box">
            <Mail size={48} className="cta-icon" />
            <h2 className="cta-box-title">{t('jobs.cta.title')}</h2>
            <p className="cta-box-description">
              {t('jobs.cta.content')}
            </p>
            <button
              className="btn btn-primary btn-lg"
              onClick={() => onNavigate('contact')}
            >
              {t('jobs.cta.button')}
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default JoinUs;


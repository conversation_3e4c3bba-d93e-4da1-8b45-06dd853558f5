/* Page Header */
.page-header {
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-navy-light) 100%);
  padding: var(--spacing-4xl) 0 var(--spacing-3xl);
  text-align: center;
}

.page-title {
  font-size: var(--text-5xl);
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.page-subtitle {
  font-size: var(--text-xl);
  color: rgba(255, 255, 255, 0.9);
  max-width: 700px;
  margin: 0 auto;
}

/* Services List */
.services-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4xl);
}

.service-detail {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.service-detail.reverse {
  direction: rtl;
}

.service-detail.reverse>* {
  direction: ltr;
}

.service-detail-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.service-detail-icon {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
}

.service-detail-icon.blue {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
}

.service-detail-icon.green {
  background: linear-gradient(135deg, var(--color-green) 0%, var(--color-green-light) 100%);
}

.service-detail-icon.navy {
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-navy-light) 100%);
}

.service-detail-title {
  font-size: var(--text-3xl);
  color: var(--color-navy);
  margin-bottom: var(--spacing-sm);
}

.service-detail-intro {
  font-size: var(--text-lg);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-md);
}

.service-features {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.service-feature {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  font-size: var(--text-base);
  color: var(--color-gray-700);
}

.feature-check {
  flex-shrink: 0;
  color: var(--color-green);
  margin-top: 2px;
}

/* Service Image */
.service-detail-image {
  width: 100%;
  height: 100%;
  min-height: 400px;
  overflow: hidden;
  border-radius: var(--radius-2xl);
}

.service-detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-2xl);
  transition: transform var(--transition-base);
}

.service-detail:hover .service-detail-image img {
  transform: scale(1.05);
}

/* Services CTA */
.services-cta {
  background-color: var(--color-gray-50);
}

.cta-box {
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-navy-dark) 100%);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-3xl);
  text-align: center;
}

.cta-box-title {
  font-size: var(--text-4xl);
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.cta-box-description {
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-2xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive */
@media (max-width: 1024px) {
  .service-detail {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .service-detail.reverse {
    direction: ltr;
  }

  .service-detail-image {
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-3xl) 0 var(--spacing-2xl);
  }

  .page-title {
    font-size: var(--text-4xl);
  }

  .page-subtitle {
    font-size: var(--text-lg);
  }

  .services-list {
    gap: var(--spacing-3xl);
  }

  .service-detail-title {
    font-size: var(--text-2xl);
  }

  .service-detail-intro {
    font-size: var(--text-base);
  }

  .service-detail-icon {
    width: 60px;
    height: 60px;
  }

  .service-detail-icon svg {
    width: 32px;
    height: 32px;
  }

  .cta-box {
    padding: var(--spacing-2xl);
  }

  .cta-box-title {
    font-size: var(--text-3xl);
  }

  .cta-box-description {
    font-size: var(--text-base);
  }
}

@media (max-width: 640px) {
  .service-detail-image {
    min-height: 250px;
  }

  .image-placeholder svg {
    width: 80px;
    height: 80px;
  }

  .service-features {
    gap: var(--spacing-sm);
  }

  .service-feature {
    font-size: var(--text-sm);
  }
}
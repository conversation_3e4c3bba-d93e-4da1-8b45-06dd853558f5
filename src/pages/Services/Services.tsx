import { useTranslation } from 'react-i18next';
import { Sparkles, Package, FileText, Check } from 'lucide-react';
import './Services.css';

interface ServicesProps {
  onNavigate: (page: string) => void;
}

const Services = ({ onNavigate }: ServicesProps) => {
  const { t } = useTranslation();

  const services = [
    {
      icon: <Sparkles size={48} />,
      title: t('services.cleaning.details.title'),
      intro: t('services.cleaning.details.intro'),
      features: t('services.cleaning.details.features', { returnObjects: true }) as string[],
      color: 'blue',
      image: '/cleaning.jpg',
    },
    {
      icon: <Package size={48} />,
      title: t('services.logistics.details.title'),
      intro: t('services.logistics.details.intro'),
      features: t('services.logistics.details.features', { returnObjects: true }) as string[],
      color: 'green',
      image: '/logistic.jpg',
    },
    {
      icon: <FileText size={48} />,
      title: t('services.administrative.details.title'),
      intro: t('services.administrative.details.intro'),
      features: t('services.administrative.details.features', { returnObjects: true }) as string[],
      color: 'navy',
      image: '/admin.jpg',
    },
  ];

  return (
    <div className="services-page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <h1 className="page-title animate-fade-in-up">{t('services.title')}</h1>
          <p className="page-subtitle animate-fade-in-up">{t('services.subtitle')}</p>
        </div>
      </section>

      {/* Services Details */}
      <section className="section">
        <div className="container">
          <div className="services-list">
            {services.map((service, index) => (
              <div
                key={service.title}
                className={`service-detail ${index % 2 === 1 ? 'reverse' : ''}`}
              >
                <div className="service-detail-content">
                  <div className={`service-detail-icon ${service.color}`}>
                    {service.icon}
                  </div>
                  <h2 className="service-detail-title">{service.title}</h2>
                  <p className="service-detail-intro">{service.intro}</p>
                  <ul className="service-features">
                    {service.features.map((feature, idx) => (
                      <li key={idx} className="service-feature">
                        <Check size={20} className="feature-check" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="service-detail-image">
                  <img src={service.image} alt={service.title} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section services-cta">
        <div className="container">
          <div className="cta-box">
            <h2 className="cta-box-title">{t('common.contactUs')}</h2>
            <p className="cta-box-description">
              {t('hero.tagline')}
            </p>
            <button
              className="btn btn-primary btn-lg"
              onClick={() => onNavigate('contact')}
            >
              {t('common.getStarted')}
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;


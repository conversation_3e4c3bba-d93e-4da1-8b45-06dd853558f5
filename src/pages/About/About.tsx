import { useTranslation } from 'react-i18next';
import { Target, Users, Award, Heart } from 'lucide-react';
import './About.css';

const About = () => {
  const { t } = useTranslation();

  // const teamMembers = t('about.team.members', { returnObjects: true }) as Array<{
    name: string;
    role: string;
    description: string;
  }>;

  const values = [
    {
      icon: <Award size={32} />,
      title: t('values.reliability.title'),
      description: t('values.reliability.description'),
    },
    {
      icon: <Target size={32} />,
      title: t('values.responsiveness.title'),
      description: t('values.responsiveness.description'),
    },
    {
      icon: <Heart size={32} />,
      title: t('values.trust.title'),
      description: t('values.trust.description'),
    },
    {
      icon: <Users size={32} />,
      title: t('values.versatility.title'),
      description: t('values.versatility.description'),
    },
  ];

  return (
    <div className="about-page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <h1 className="page-title animate-fade-in-up">{t('about.title')}</h1>
          <p className="page-subtitle animate-fade-in-up">{t('about.subtitle')}</p>
        </div>
      </section>

      {/* Story Section */}
      <section className="section about-story">
        <div className="container">
          <div className="about-content">
            <div className="about-text">
              <h2 className="about-section-title">{t('about.story.title')}</h2>
              <p className="about-section-content">{t('about.story.content')}</p>
            </div>
            <div className="about-image">
              <img src="/Lausanne.jpg" alt="Lausanne, Switzerland" />
              <div className="about-image-overlay">
                <div className="about-year">2021</div>
                <div className="about-location">Lausanne</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="section about-mission">
        <div className="container">
          <div className="about-content reverse">
            <div className="about-image">
              <img src="/mission.jpg" alt={t('about.mission.title')} />
            </div>
            <div className="about-text">
              <h2 className="about-section-title">{t('about.mission.title')}</h2>
              <p className="about-section-content">{t('about.mission.content')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="section about-values">
        <div className="container">
          <div className="section-title">
            <h2>{t('values.title')}</h2>
          </div>
          <div className="values-grid-about">
            {values.map((value, index) => (
              <div
                key={value.title}
                className="value-card-about"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="value-icon-about">{value.icon}</div>
                <h3 className="value-title-about">{value.title}</h3>
                <p className="value-description-about">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      {/*
      <section className="section about-team-section">
        <div className="container">
          <div className="section-title">
            <h2>{t('about.team.title')}</h2>
            <p>{t('about.team.content')}</p>
          </div>
          <div className="team-grid">
            {teamMembers.map((member, index) => (
              <div
                key={member.name}
                className="team-member-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="team-member-image">
                  <User size={64} />
                </div>
                <h3 className="team-member-name">{member.name}</h3>
                <p className="team-member-role">{member.role}</p>
                <p className="team-member-description">{member.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      */}
    </div>
  );
};

export default About;


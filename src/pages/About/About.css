/* About Content Layout */
.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.about-content.reverse {
  direction: rtl;
}

.about-content.reverse>* {
  direction: ltr;
}

.about-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.about-section-title {
  font-size: var(--text-3xl);
  color: var(--color-navy);
  margin-bottom: var(--spacing-md);
}

.about-section-content {
  font-size: var(--text-lg);
  color: var(--color-gray-600);
  line-height: 1.8;
}

/* About Images */
.about-image {
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-2xl);
}

.about-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-2xl);
  transition: transform var(--transition-base);
}

.about-image:hover img {
  transform: scale(1.05);
}

.about-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(30, 58, 95, 0.7) 0%, rgba(30, 58, 95, 0.5) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  color: var(--color-white);
  border-radius: var(--radius-2xl);
}

.about-year {
  font-size: 5rem;
  font-weight: 700;
  font-family: var(--font-heading);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.about-location {
  font-size: var(--text-2xl);
  font-weight: 500;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Story Section */
.about-story {
  background-color: var(--color-white);
}

/* Mission Section */
.about-mission {
  background-color: var(--color-gray-50);
}

/* Values Section */
.about-values {
  background-color: var(--color-white);
}

.values-grid-about {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
}

.value-card-about {
  background-color: var(--color-white);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all var(--transition-base);
  animation: fadeInUp 0.6s ease-out both;
}

.value-card-about:hover {
  border-color: var(--color-green);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.value-icon-about {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--color-green) 0%, var(--color-green-light) 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  margin: 0 auto var(--spacing-lg);
  transition: transform var(--transition-base);
}

.value-card-about:hover .value-icon-about {
  transform: scale(1.1) rotate(5deg);
}

.value-title-about {
  font-size: var(--text-xl);
  color: var(--color-navy);
  margin-bottom: var(--spacing-sm);
}

.value-description-about {
  font-size: var(--text-base);
  color: var(--color-gray-600);
}

/* Team Section */
.about-team-section {
  background-color: var(--color-white);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-2xl);
}

.team-member-card {
  background-color: var(--color-white);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  transition: all var(--transition-base);
  animation: fadeInUp 0.6s ease-out both;
}

.team-member-card:hover {
  border-color: var(--color-green);
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.team-member-image {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-navy-light) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  margin: 0 auto var(--spacing-lg);
  transition: transform var(--transition-base);
}

.team-member-card:hover .team-member-image {
  transform: scale(1.1);
}

.team-member-name {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--color-navy);
  margin-bottom: var(--spacing-xs);
}

.team-member-role {
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--color-green);
  margin-bottom: var(--spacing-md);
}

.team-member-description {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  line-height: 1.6;
}

/* Responsive */
@media (max-width: 1024px) {
  .values-grid-about {
    grid-template-columns: repeat(2, 1fr);
  }

  .team-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .about-content.reverse {
    direction: ltr;
  }

  .about-image {
    min-height: 300px;
  }

  .about-section-title {
    font-size: var(--text-2xl);
  }

  .about-section-content {
    font-size: var(--text-base);
  }

  .about-year {
    font-size: 4rem;
  }

  .about-location {
    font-size: var(--text-xl);
  }

  .values-grid-about {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .team-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 640px) {
  .about-image {
    min-height: 250px;
  }

  .about-year {
    font-size: 3rem;
  }

  .about-location {
    font-size: var(--text-lg);
  }

  .value-icon-about {
    width: 48px;
    height: 48px;
  }

  .value-icon-about svg {
    width: 24px;
    height: 24px;
  }

  .team-member-image {
    width: 100px;
    height: 100px;
  }

  .team-member-image svg {
    width: 48px;
    height: 48px;
  }
}
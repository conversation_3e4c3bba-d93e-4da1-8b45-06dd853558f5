.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background-color: var(--color-white);
  transition: all var(--transition-base);
  border-bottom: 1px solid transparent;
}

.header-scrolled {
  box-shadow: var(--shadow-md);
  border-bottom-color: var(--color-gray-200);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) 0;
  transition: padding var(--transition-base);
}

.header-scrolled .header-content {
  padding: var(--spacing-md) 0;
}

/* Logo */
.header-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  transition: opacity var(--transition-fast);
}

.header-logo:hover {
  opacity: 0.8;
}

.header-logo img {
  height: 40px;
  width: auto;
}

.header-logo-text {
  font-family: var(--font-heading);
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--color-navy);
  letter-spacing: 0.5px;
}

/* Desktop Navigation */
.header-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav-link {
  position: relative;
  background: none;
  color: var(--color-gray-700);
  font-weight: 500;
  font-size: var(--text-base);
  padding: var(--spacing-sm) 0;
  transition: color var(--transition-fast);
}

.nav-link:hover {
  color: var(--color-green);
}

.nav-link.active {
  color: var(--color-navy);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-green);
  border-radius: var(--radius-full);
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.language-switcher {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-gray-100);
  color: var(--color-navy);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
}

.language-switcher:hover {
  background-color: var(--color-green);
  color: var(--color-white);
}

.mobile-menu-toggle {
  display: none;
  background: none;
  color: var(--color-navy);
  padding: var(--spacing-sm);
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) 0;
  border-top: 1px solid var(--color-gray-200);
  animation: fadeInDown 0.3s ease-out;
}

.mobile-nav-link {
  width: 100%;
  text-align: left;
  background: none;
  color: var(--color-gray-700);
  font-weight: 500;
  font-size: var(--text-lg);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.mobile-nav-link:hover {
  background-color: var(--color-gray-100);
  color: var(--color-green);
}

.mobile-nav-link.active {
  background-color: var(--color-green);
  color: var(--color-white);
}

/* Responsive */
@media (max-width: 1024px) {
  .header-nav {
    gap: var(--spacing-lg);
  }

  .nav-link {
    font-size: var(--text-sm);
  }
}

@media (max-width: 768px) {
  .header-nav {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .mobile-nav {
    display: flex;
  }

  .header-logo img {
    height: 32px;
  }

  .header-logo-text {
    font-size: var(--text-lg);
  }
}

@media (max-width: 640px) {
  .header-content {
    padding: var(--spacing-md) 0;
  }

  .header-scrolled .header-content {
    padding: var(--spacing-sm) 0;
  }

  .language-switcher span {
    display: none;
  }
}


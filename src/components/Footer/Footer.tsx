import { useTranslation } from 'react-i18next';
import { Mail, Phone, MapPin } from 'lucide-react';
import './Footer.css';

interface FooterProps {
  onNavigate: (page: string) => void;
}

const Footer = ({ onNavigate }: FooterProps) => {
  const { t } = useTranslation();

  const quickLinks = [
    { key: 'home', label: t('nav.home') },
    { key: 'services', label: t('nav.services') },
    { key: 'about', label: t('nav.about') },
    { key: 'joinUs', label: t('nav.joinUs') },
    { key: 'contact', label: t('nav.contact') },
  ];

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          {/* Brand Section */}
          <div className="footer-section footer-brand">
            <div className="footer-logo">
              <img src="/teralis.png" alt="TERALIS" />
            </div>
            <p className="footer-tagline">{t('footer.tagline')}</p>
          </div>

          {/* Quick Links */}
          <div className="footer-section">
            <h4 className="footer-title">{t('footer.quickLinks')}</h4>
            <ul className="footer-links">
              {quickLinks.map((link) => (
                <li key={link.key}>
                  <button
                    onClick={() => onNavigate(link.key)}
                    className="footer-link"
                  >
                    {link.label}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="footer-section">
            <h4 className="footer-title">{t('footer.contactInfo')}</h4>
            <ul className="footer-contact">
              <li>
                <Mail size={18} />
                <a href="mailto:<EMAIL>">
                  <EMAIL>
                </a>
              </li>
              <li>
                <Phone size={18} />
                <a href="tel:+41215129845">+41 (0)21 512 98 45</a>
              </li>
              <li>
                <MapPin size={18} />
                <span>{t('footer.location')}</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="footer-bottom">
          <p className="footer-copyright">© 2021 TERALIS SA. {t('footer.rightsText')}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;


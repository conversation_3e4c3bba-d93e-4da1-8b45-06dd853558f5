.footer {
  background-color: var(--color-navy);
  color: var(--color-white);
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
  margin-top: var(--spacing-4xl);
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: var(--spacing-3xl);
  margin-bottom: var(--spacing-3xl);
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* Brand Section */
.footer-brand {
  max-width: 400px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.footer-logo img {
  height: 40px;
  width: auto;
  filter: brightness(0) invert(1);
}

.footer-logo-text {
  font-family: var(--font-heading);
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--color-white);
  letter-spacing: 0.5px;
}

.footer-tagline {
  color: var(--color-gray-300);
  font-size: var(--text-base);
  line-height: 1.6;
  margin: 0;
}

/* Footer Titles */
.footer-title {
  font-family: var(--font-heading);
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-white);
  margin-bottom: var(--spacing-sm);
}

/* Links */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-link {
  background: none;
  color: var(--color-gray-300);
  font-size: var(--text-base);
  text-align: left;
  padding: var(--spacing-xs) 0;
  transition: color var(--transition-fast);
}

.footer-link:hover {
  color: var(--color-green-light);
}

/* Contact Info */
.footer-contact {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footer-contact li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--color-gray-300);
  font-size: var(--text-base);
}

.footer-contact a {
  color: var(--color-gray-300);
  transition: color var(--transition-fast);
}

.footer-contact a:hover {
  color: var(--color-green-light);
}

/* Bottom Bar */
.footer-bottom {
  padding-top: var(--spacing-xl);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.footer-copyright {
  color: var(--color-gray-400);
  font-size: var(--text-sm);
  margin: 0;
}

/* Responsive */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
  }

  .footer-brand {
    grid-column: 1 / -1;
    max-width: 100%;
  }
}

@media (max-width: 640px) {
  .footer {
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
    margin-top: var(--spacing-3xl);
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
  }

  .footer-brand {
    grid-column: 1;
  }

  .footer-logo img {
    height: 32px;
  }

  .footer-logo-text {
    font-size: var(--text-lg);
  }

  .footer-bottom {
    padding-top: var(--spacing-lg);
  }
}


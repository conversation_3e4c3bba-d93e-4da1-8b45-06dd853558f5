# TERALIS Website - Quick Start Guide

Welcome! This guide will help you get the TERALIS website up and running in minutes.

## 🎯 What You Have

A complete, modern, bilingual website for TERALIS with:
- 5 pages (Home, Services, About, Join Us, Contact)
- French and English language support
- Mobile-responsive design
- Professional animations and interactions
- Contact form with validation

## 🚀 Getting Started (3 Steps)

### Step 1: Install Node.js (if not already installed)

Download and install Node.js from [nodejs.org](https://nodejs.org/)
- Choose the LTS (Long Term Support) version
- Version 18 or higher is required

Verify installation:
```bash
node --version
npm --version
```

### Step 2: Install Dependencies

Open a terminal in the project folder and run:
```bash
npm install
```

This will download all necessary packages (takes 1-2 minutes).

### Step 3: Start the Website

```bash
npm run dev
```

The website will open at: **http://localhost:5173** (or 5174 if 5173 is busy)

🎉 **That's it!** Your website is now running locally.

## 🌐 Viewing the Website

Once running, you can:
- Navigate between pages using the menu
- Switch between French and English using the language button (FR/EN)
- Test the contact form
- View on mobile by opening the URL on your phone (same network)

## 📱 Testing on Mobile

1. Make sure your computer and phone are on the same WiFi
2. Find your computer's IP address:
   - Windows: `ipconfig` (look for IPv4)
   - Mac/Linux: `ifconfig` (look for inet)
3. On your phone, open: `http://YOUR_IP:5173`

## 🛠️ Making Changes

### Changing Text Content

All text is in translation files:
- **French**: `src/i18n/locales/fr.json`
- **English**: `src/i18n/locales/en.json`

Edit these files to change any text on the website. The changes will appear immediately!

### Changing Colors

Colors are defined in `src/index.css` at the top:
```css
--color-navy: #1e3a5f;      /* Main brand color */
--color-green: #7cb342;     /* Accent color */
```

### Changing the Logo

Replace `assets/teralis_logo.png` with your new logo (keep the same filename).

## 🏗️ Building for Production

When ready to deploy:

```bash
npm run build
```

This creates a `dist` folder with optimized files ready for hosting.

## 📤 Deploying to the Internet

### Option 1: Netlify (Easiest - Free)

1. Go to [netlify.com](https://www.netlify.com) and sign up
2. Drag and drop the `dist` folder
3. Your site is live! You'll get a URL like: `your-site.netlify.app`
4. (Optional) Add your custom domain in settings

### Option 2: Vercel (Also Easy - Free)

1. Go to [vercel.com](https://vercel.com) and sign up
2. Import your project
3. Vercel will automatically build and deploy
4. You'll get a URL like: `your-site.vercel.app`

For detailed deployment instructions, see `DEPLOYMENT.md`.

## 📞 Contact Information on the Website

The website displays:
- **Email**: <EMAIL>
- **Phone**: +41 (0)21 512 98 45
- **Location**: Lausanne, Switzerland

To change these, edit the translation files mentioned above.

## ⚠️ Important Notes

### Contact Form
The contact form currently **simulates** submission (shows success message but doesn't send email). To make it functional:
1. You'll need a backend service or email API
2. Options: EmailJS, Formspree, or custom backend
3. See `DEPLOYMENT.md` for integration options

### Map
The map on the Contact page is a placeholder. To add a real map:
1. Get a Google Maps API key
2. Integrate Google Maps component
3. Or use an embedded map iframe

## 🎨 Customization Tips

### Adding a New Page

1. Create a new folder in `src/pages/` (e.g., `Blog/`)
2. Add `Blog.tsx` and `Blog.css` files
3. Add translations in `fr.json` and `en.json`
4. Update `App.tsx` to include the new page
5. Add navigation link in `Header.tsx`

### Changing Fonts

Fonts are loaded from Google Fonts. To change:
1. Go to [fonts.google.com](https://fonts.google.com)
2. Select your fonts
3. Update the import in `src/index.css`
4. Update the CSS variables

## 🐛 Troubleshooting

### "npm: command not found"
- Node.js is not installed. Install from [nodejs.org](https://nodejs.org/)

### Port already in use
- Vite will automatically try the next port (5174, 5175, etc.)
- Or stop other applications using port 5173

### Changes not showing
- Hard refresh: Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)
- Clear browser cache
- Restart the dev server

### Build errors
- Delete `node_modules` folder
- Delete `package-lock.json`
- Run `npm install` again
- Run `npm run build`

## 📚 Additional Resources

- **Full Documentation**: See `README.md`
- **Deployment Guide**: See `DEPLOYMENT.md`
- **Project Details**: See `PROJECT_SUMMARY.md`

## 🆘 Getting Help

If you encounter issues:
1. Check the error message in the terminal
2. Review the documentation files
3. Search for the error online
4. Contact your developer

## ✅ Checklist Before Going Live

- [ ] Test all pages load correctly
- [ ] Verify language switching works
- [ ] Test on mobile devices
- [ ] Check all links work
- [ ] Verify contact information is correct
- [ ] Test contact form validation
- [ ] Check logo displays correctly
- [ ] Review all text content
- [ ] Test in different browsers
- [ ] Optimize images if needed
- [ ] Set up custom domain (optional)
- [ ] Configure contact form backend (if needed)

## 🎉 You're All Set!

Your TERALIS website is modern, professional, and ready to impress. The bilingual functionality makes it accessible to both French and English speakers, and the responsive design ensures it looks great on all devices.

**Need to make updates?** Just edit the translation files and rebuild!

**Ready to deploy?** Follow the deployment guide and your site will be live in minutes!

---

**Questions?** Contact: <EMAIL>

**Last Updated**: 2025-10-01

